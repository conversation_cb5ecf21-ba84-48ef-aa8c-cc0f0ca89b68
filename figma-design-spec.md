# AI 自动阅卷助手 - Figma 设计规范

## 整体布局
- **画布尺寸**: 1440x900px (桌面应用标准尺寸)
- **主容器**: 两栏布局，左侧配置区占40%，右侧监控预览区占60%

## 设计系统

### 颜色规范
- **主色调**: #409EFF (Element Plus 蓝色)
- **成功色**: #67C23A
- **警告色**: #E6A23C
- **危险色**: #F56C6C
- **背景色**: #F5F7FA
- **卡片背景**: #FFFFFF
- **边框色**: #DCDFE6
- **文字主色**: #303133
- **文字次色**: #606266

### 字体规范
- **标题**: 16px, 字重 600
- **正文**: 14px, 字重 400
- **小字**: 12px, 字重 400

## 组件详细设计

### 1. 顶部标题栏
- **高度**: 60px
- **背景**: 渐变色 #409EFF 到 #66B3FF
- **标题**: "AI 自动阅卷助手 v1.0" (18px, 白色, 居左)
- **窗口控制按钮**: 最小化、最大化、关闭 (右上角)

### 2. 左侧配置区 (宽度: 576px)

#### 2.1 阅卷配置卡片
- **卡片标题**: "阅卷配置"
- **内边距**: 20px
- **圆角**: 8px

##### 浏览器自动化设置区域
- **小标题**: "1. 浏览器自动化设置" (16px, 字重 500)
- **阅卷网址输入框**:
  - 标签: "阅卷网址"
  - 输入框宽度: 100%
  - 高度: 40px
  - 占位符: "https://exam.school.com/grade?id=..."

- **截图区域设置**:
  - 标签: "截图区域"
  - 只读输入框: "X:120, Y:300, W:800, H:600"
  - 两个按钮并排:
    - "选择区域" (主要按钮样式)
    - "预览" (次要按钮样式)

- **分数输入框选择器**:
  - 标签: "分数输入框 Selector"
  - 输入框 + "拾取元素" 按钮组合

- **翻页按钮选择器**:
  - 标签: "翻页按钮 Selector"
  - 输入框 + "拾取元素" 按钮组合

##### AI 评分标准区域
- **小标题**: "2. AI 评分标准" (16px, 字重 500)
- **科目选择**: 下拉框，默认值 "高中地理"
- **阅卷模式**: 下拉框，默认值 "专业模式"
- **评分标准**: 多行文本框，高度 120px

### 3. 右侧监控预览区 (宽度: 864px)

#### 3.1 控制与状态卡片
- **高度**: 120px
- **布局**: 左右分布
- **左侧信息**:
  - "阅卷数量: 12 份" (输入框样式)
  - "当前状态: 已批阅" (状态文字)
- **右侧按钮**:
  - "开始阅卷" 大按钮 (宽度 200px, 高度 50px)

#### 3.2 实时预览卡片
- **高度**: 300px
- **图片预览区域**: 
  - 背景: #F8F9FA
  - 边框: 虚线边框
  - 居中显示截图
- **AI分析结果区域**:
  - 标签: "AI分析结果"
  - 只读文本区域，显示评分和理由

#### 3.3 运行日志卡片
- **高度**: 剩余空间
- **标签页**: "运行日志" | "AI详细响应"
- **日志列表**: 
  - 可滚动区域
  - 每条日志包含时间戳、级别、消息
  - 不同级别用不同颜色标识

## 交互状态设计

### 按钮状态
- **默认**: 正常颜色
- **悬停**: 颜色加深 10%
- **点击**: 颜色加深 20%
- **禁用**: 灰色，透明度 50%

### 输入框状态
- **默认**: 边框 #DCDFE6
- **聚焦**: 边框 #409EFF，阴影效果
- **错误**: 边框 #F56C6C

### 卡片阴影
- **默认**: box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1)
- **悬停**: box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15)

## 响应式考虑
- 最小宽度: 1200px
- 当窗口缩小时，保持左右比例，但确保最小可用空间

## 图标使用
- 使用 Element Plus 图标库
- 按钮图标大小: 16px
- 状态图标大小: 14px

## 动画效果
- 页面切换: 300ms ease-in-out
- 按钮点击: 150ms ease
- 卡片展开/收起: 250ms ease-in-out