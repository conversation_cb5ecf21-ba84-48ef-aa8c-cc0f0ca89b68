# Figma 实现指南

## 第一步：创建画布和基础框架

1. **新建文件**: 在 Figma 中创建新文件，命名为 "AI 自动阅卷助手界面设计"

2. **设置画布**: 
   - 创建 Frame，尺寸设为 1440x900px
   - 命名为 "Main Interface"

3. **创建基础布局**:
   ```
   Main Interface (1440x900)
   ├── Header (1440x60)
   ├── Content Area (1440x840)
   │   ├── Left Panel (576x840)
   │   └── Right Panel (864x840)
   ```

## 第二步：设计顶部标题栏

1. **创建 Header Frame** (1440x60):
   - 背景: 线性渐变 #409EFF → #66B3FF
   - 添加文字 "AI 自动阅卷助手 v1.0"
   - 字体: 18px, 白色, 左对齐，左边距 20px

2. **添加窗口控制按钮**:
   - 三个圆形按钮 (12x12px)
   - 颜色: 最小化(#FFBD2E), 最大化(#28CA42), 关闭(#FF5F56)
   - 右边距 20px

## 第三步：左侧配置区设计

### 3.1 创建配置卡片容器
```
Left Panel (576x840)
├── Configuration Card (536x800, margin: 20px)
```

### 3.2 浏览器自动化设置区域
1. **区域标题**: "1. 浏览器自动化设置"
2. **阅卷网址输入**:
   - Label: "阅卷网址" (14px, #606266)
   - Input: 高度 40px, 圆角 4px, 边框 #DCDFE6
3. **截图区域设置**:
   - Label + 只读输入框
   - 两个按钮: "选择区域"(主按钮) + "预览"(次按钮)
4. **选择器设置**: 重复输入框 + 按钮的组合

### 3.3 AI 评分标准区域
1. **区域标题**: "2. AI 评分标准"
2. **下拉选择框**: 科目和阅卷模式
3. **多行文本框**: 评分标准输入，高度 120px

## 第四步：右侧监控预览区设计

### 4.1 控制与状态卡片 (844x120)
- 左侧: 阅卷数量显示 + 状态文字
- 右侧: "开始阅卷" 大按钮 (200x50px)

### 4.2 实时预览卡片 (844x300)
- 上半部分: 图片预览区域 (虚线边框矩形)
- 下半部分: AI分析结果文本区域

### 4.3 运行日志卡片 (844x剩余高度)
- Tab 标签: "运行日志" | "AI详细响应"
- 可滚动的日志列表区域

## 第五步：创建组件库

### 5.1 按钮组件
- **主要按钮**: #409EFF 背景，白色文字，8px 圆角
- **次要按钮**: 白色背景，#409EFF 边框和文字
- **危险按钮**: #F56C6C 背景，白色文字

### 5.2 输入框组件
- **标准输入框**: 40px 高度，4px 圆角，#DCDFE6 边框
- **多行文本框**: 可调整高度，相同样式
- **只读输入框**: 灰色背景 #F5F7FA

### 5.3 卡片组件
- **标准卡片**: 白色背景，8px 圆角，阴影效果
- **标题**: 16px 字重 600，#303133 颜色

## 第六步：添加交互状态

1. **按钮状态**:
   - Default, Hover, Pressed, Disabled
   - 使用 Figma 的 Variants 功能

2. **输入框状态**:
   - Default, Focus, Error
   - 聚焦状态添加蓝色边框和阴影

## 第七步：创建原型

1. **添加交互热区**:
   - 按钮点击效果
   - 输入框聚焦效果
   - Tab 切换效果

2. **设置过渡动画**:
   - Smart Animate
   - 持续时间 300ms
   - Ease In and Out

## 第八步：导出和交付

1. **创建设计规范页面**:
   - 颜色板
   - 字体规范
   - 间距规范
   - 组件库

2. **导出资源**:
   - PNG 格式的界面截图
   - SVG 格式的图标
   - CSS 代码片段

## 实用技巧

1. **使用 Auto Layout**: 让组件自动适应内容变化
2. **创建 Styles**: 统一颜色、文字、阴影样式
3. **使用 Constraints**: 确保响应式布局
4. **组件嵌套**: 创建可复用的复合组件
5. **命名规范**: 使用清晰的图层命名，便于开发对接