package api

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestNewClient(t *testing.T) {
	// 跳过这个测试，因为它需要 Wails 运行时上下文
	t.Skip("Skipping client test - requires Wails runtime context")
}

func TestTokenManager(t *testing.T) {
	tm := NewTokenManager()

	// 测试初始状态
	if tm.HasValidToken() {
		t.Error("Should not have valid token initially")
	}

	if tm.GetAccessToken() != "" {
		t.Error("Should not have access token initially")
	}

	// 测试设置令牌
	tokenInfo := &TokenInfo{
		AccessToken:  "test-access-token",
		RefreshToken: "test-refresh-token",
		ExpiresAt:    time.Now().Unix() + 3600,
		ExpiresIn:    3600,
	}

	tm.SetTokens(tokenInfo)

	// 测试获取令牌
	if !tm.HasValidToken() {
		t.Error("Should have valid token after setting")
	}

	if tm.GetAccessToken() != "test-access-token" {
		t.<PERSON>rror("Access token mismatch")
	}

	if tm.GetRefreshToken() != "test-refresh-token" {
		t.Error("Refresh token mismatch")
	}

	// 测试清除令牌
	tm.ClearTokens()

	if tm.HasValidToken() {
		t.Error("Should not have valid token after clearing")
	}
}

func TestHTTPClient(t *testing.T) {
	// 创建测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/test":
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"message": "success"}`))
		case "/error":
			w.WriteHeader(http.StatusBadRequest)
			w.Write([]byte(`{"error": "bad request"}`))
		case "/auth-error":
			w.WriteHeader(http.StatusUnauthorized)
			w.Write([]byte(`{"message": "unauthorized"}`))
		default:
			w.WriteHeader(http.StatusNotFound)
		}
	}))
	defer server.Close()

	config := HTTPConfig{
		BaseURL:    server.URL,
		Timeout:    5 * time.Second,
		Retries:    1,
		RetryDelay: 100 * time.Millisecond,
		Headers:    make(map[string]string),
	}

	client := NewHTTPClient(config)
	ctx := context.Background()

	// 测试成功请求
	var result map[string]any
	err := client.RequestAndDecode(ctx, "GET", "/test", nil, &result, nil)
	if err != nil {
		t.Fatalf("Failed to make successful request: %v", err)
	}

	if result["message"] != "success" {
		t.Error("Unexpected response message")
	}

	// 测试错误请求
	err = client.RequestAndDecode(ctx, "GET", "/error", nil, &result, nil)
	if err == nil {
		t.Error("Should have returned error for bad request")
	}

	if _, ok := err.(*ValidationError); !ok {
		t.Errorf("Expected ValidationError, got %T", err)
	}

	// 测试认证错误
	err = client.RequestAndDecode(ctx, "GET", "/auth-error", nil, &result, nil)
	if err == nil {
		t.Error("Should have returned error for auth error")
	}

	if _, ok := err.(*AuthenticationError); !ok {
		t.Errorf("Expected AuthenticationError, got %T", err)
	}
}

func TestAuthService(t *testing.T) {
	// 跳过这个测试，因为它需要 Wails 运行时上下文
	t.Skip("Skipping auth service test - requires Wails runtime context")
}

func TestBusinessService(t *testing.T) {
	// 跳过这个测试，因为它需要 Wails 运行时上下文
	t.Skip("Skipping business service test - requires Wails runtime context")
}
