# API 服务使用指南

## 🚀 新特性

### 1. 腾讯云 CAM 鉴权支持
- 自动添加 TC3-HMAC-SHA256 签名
- 支持腾讯云函数调用
- 自动处理时间戳和授权头

### 2. 内存 Token 管理
- 不再保存到文件，仅内存存储
- 应用重启后需要重新登录
- 更安全的令牌管理

### 3. 结构化日志
- 使用 Go 标准库 slog
- 结构化日志输出
- 更好的调试体验

## 📖 使用示例

### 1. 创建客户端

```go
import (
    "context"
    "ai-grading/internal/api"
)

// 创建基础客户端（无签名）
client, err := api.NewClient(ctx, "https://api.example.com")
if err != nil {
    log.Fatal(err)
}

// 创建带腾讯云凭证的客户端（自动签名）
client, err := api.NewClientWithCredentials(ctx, 
    "https://scf.tencentcloudapi.com", 
    "your-secret-id", 
    "your-secret-key")
if err != nil {
    log.Fatal(err)
}
```

### 2. 认证操作

```go
// 用户登录
loginResp, err := client.Login("<EMAIL>", "password")
if err != nil {
    log.Printf("Login failed: %v", err)
    return
}

// 邮箱验证码登录
codeResp, err := client.SendEmailCode("<EMAIL>")
if err != nil {
    log.Printf("Send code failed: %v", err)
    return
}

emailLoginResp, err := client.EmailLogin("<EMAIL>", "123456")
if err != nil {
    log.Printf("Email login failed: %v", err)
    return
}

// 检查认证状态
if client.IsAuthenticated() {
    fmt.Println("User is authenticated")
}

// 手动刷新令牌
refreshResp, err := client.RefreshToken()
if err != nil {
    log.Printf("Refresh failed: %v", err)
}
```

### 3. 业务操作

```go
// 查询钱包余额
balance, err := client.GetWalletBalance()
if err != nil {
    log.Printf("Get balance failed: %v", err)
    return
}
fmt.Printf("Balance: %.2f %s\n", balance.Balance, balance.Currency)

// AI 图像分析
analysis, err := client.AnalyzeImage(
    "base64-image-data",
    "grading-criteria", 
    "math",
    "detailed")
if err != nil {
    log.Printf("Analysis failed: %v", err)
    return
}
fmt.Printf("Score: %d\n", analysis.Score)

// 获取应用版本
version, err := client.GetAppVersion()
if err != nil {
    log.Printf("Get version failed: %v", err)
    return
}
fmt.Printf("Version: %s\n", version.Version)

// 提交充值申请
recharge, err := client.SubmitRechargeRequest(100.0, "alipay")
if err != nil {
    log.Printf("Recharge failed: %v", err)
    return
}
fmt.Printf("Recharge ID: %s\n", recharge.RequestID)
```

### 4. 配置管理

```go
// 在配置文件中设置腾讯云凭证
config := &config.Config{
    API: config.APIConfig{
        BaseURL:   "https://scf.tencentcloudapi.com",
        SecretID:  "your-secret-id",
        SecretKey: "your-secret-key",
    },
}

// 应用会自动使用这些凭证创建带签名的客户端
```

### 5. 错误处理

```go
// 类型化错误处理
_, err := client.Login("<EMAIL>", "wrong-password")
if err != nil {
    switch e := err.(type) {
    case *api.AuthenticationError:
        fmt.Printf("Authentication failed: %s\n", e.Message)
    case *api.NetworkError:
        fmt.Printf("Network error: %s\n", e.Message)
    case *api.APIError:
        fmt.Printf("API error [%d]: %s\n", e.StatusCode, e.Message)
    default:
        fmt.Printf("Unknown error: %v\n", err)
    }
}
```

## 🔧 配置说明

### API 配置

```json
{
  "api": {
    "base_url": "https://scf.tencentcloudapi.com",
    "timeout_seconds": 30,
    "retries": 3,
    "retry_delay_ms": 1000,
    "secret_id": "your-tencent-cloud-secret-id",
    "secret_key": "your-tencent-cloud-secret-key"
  }
}
```

### 环境变量

```bash
# 可选：通过环境变量设置凭证
export TENCENT_SECRET_ID="your-secret-id"
export TENCENT_SECRET_KEY="your-secret-key"
```

## 🔍 调试

### 日志输出

```go
import "log/slog"

// 设置日志级别
slog.SetDefault(slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
    Level: slog.LevelDebug,
})))

// API 调用会自动输出结构化日志
client.Login("<EMAIL>", "password")
// 输出: INFO Login successful email=<EMAIL>
```

### 网络调试

```go
// 检查连接状态
if client.CheckConnection() {
    fmt.Println("API connection is healthy")
} else {
    fmt.Println("API connection failed")
}

// 健康检查
health := client.HealthCheck()
fmt.Printf("Health: %+v\n", health)
```

## ⚠️ 注意事项

1. **Token 管理**: 令牌仅存储在内存中，应用重启后需要重新登录
2. **腾讯云签名**: 确保 SecretID 和 SecretKey 正确配置
3. **网络重试**: 网络错误会自动重试，认证错误不会重试
4. **并发安全**: 所有方法都是并发安全的
5. **错误处理**: 建议使用类型化错误处理来提供更好的用户体验

## 🔗 相关文档

- [API 接口文档](./README.md)
- [类型定义](./types.go)
- [测试用例](./client_test.go)
- [配置管理](../config/config.go)
