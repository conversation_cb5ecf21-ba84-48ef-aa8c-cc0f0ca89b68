package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// Config 应用配置
type Config struct {
	API     APIConfig     `json:"api"`
	App     AppConfig     `json:"app"`
	Browser BrowserConfig `json:"browser"`
	Grading GradingConfig `json:"grading"`
}

// APIConfig API配置
type APIConfig struct {
	BaseURL        string `json:"base_url"`
	TimeoutSeconds int    `json:"timeout_seconds"`
	Retries        int    `json:"retries"`
	RetryDelay     int    `json:"retry_delay_ms"`
	SecretID       string `json:"secret_id"`
	SecretKey      string `json:"secret_key"`
}

// AppConfig 应用配置
type AppConfig struct {
	Version     string `json:"version"`
	Environment string `json:"environment"`
	LogLevel    string `json:"log_level"`
	DataDir     string `json:"data_dir"`
}

// BrowserConfig 浏览器配置
type BrowserConfig struct {
	Headless     bool   `json:"headless"`
	UserAgent    string `json:"user_agent"`
	WindowWidth  int    `json:"window_width"`
	WindowHeight int    `json:"window_height"`
}

// GradingConfig 阅卷配置
type GradingConfig struct {
	DefaultSubject   string   `json:"default_subject"`
	DefaultCriteria  string   `json:"default_criteria"`
	AnalysisMode     string   `json:"analysis_mode"`
	MaxImageSize     int      `json:"max_image_size"`
	SupportedFormats []string `json:"supported_formats"`
}

// Manager 配置管理器
type Manager struct {
	configPath string
	config     *Config
}

// NewManager 创建新的配置管理器
func NewManager(configDir string) *Manager {
	configPath := filepath.Join(configDir, "config.json")
	return &Manager{
		configPath: configPath,
	}
}

// Load 加载配置
func (m *Manager) Load() (*Config, error) {
	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(m.configPath); os.IsNotExist(err) {
		config := m.getDefaultConfig()
		if err := m.Save(config); err != nil {
			return nil, fmt.Errorf("save default config: %w", err)
		}
		m.config = config
		return config, nil
	}

	data, err := os.ReadFile(m.configPath)
	if err != nil {
		return nil, fmt.Errorf("read config file: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("unmarshal config: %w", err)
	}

	m.config = &config
	return &config, nil
}

// Save 保存配置
func (m *Manager) Save(config *Config) error {
	// 确保配置目录存在
	configDir := filepath.Dir(m.configPath)
	if err := os.MkdirAll(configDir, 0o755); err != nil {
		return fmt.Errorf("create config directory: %w", err)
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("marshal config: %w", err)
	}

	if err := os.WriteFile(m.configPath, data, 0o644); err != nil {
		return fmt.Errorf("write config file: %w", err)
	}

	m.config = config
	return nil
}

// Get 获取当前配置
func (m *Manager) Get() *Config {
	if m.config == nil {
		config, _ := m.Load()
		return config
	}
	return m.config
}

// UpdateAPI 更新API配置
func (m *Manager) UpdateAPI(apiConfig APIConfig) error {
	config := m.Get()
	if config == nil {
		config = m.getDefaultConfig()
	}

	config.API = apiConfig
	return m.Save(config)
}

// UpdateApp 更新应用配置
func (m *Manager) UpdateApp(appConfig AppConfig) error {
	config := m.Get()
	if config == nil {
		config = m.getDefaultConfig()
	}

	config.App = appConfig
	return m.Save(config)
}

// UpdateBrowser 更新浏览器配置
func (m *Manager) UpdateBrowser(browserConfig BrowserConfig) error {
	config := m.Get()
	if config == nil {
		config = m.getDefaultConfig()
	}

	config.Browser = browserConfig
	return m.Save(config)
}

// UpdateGrading 更新阅卷配置
func (m *Manager) UpdateGrading(gradingConfig GradingConfig) error {
	config := m.Get()
	if config == nil {
		config = m.getDefaultConfig()
	}

	config.Grading = gradingConfig
	return m.Save(config)
}

// getDefaultConfig 获取默认配置
func (m *Manager) getDefaultConfig() *Config {
	return &Config{
		API: APIConfig{
			BaseURL:        "https://api.example.com",
			TimeoutSeconds: 30,
			Retries:        3,
			RetryDelay:     1000,
			SecretID:       "",
			SecretKey:      "",
		},
		App: AppConfig{
			Version:     "1.0.0",
			Environment: "production",
			LogLevel:    "info",
			DataDir:     "",
		},
		Browser: BrowserConfig{
			Headless:     false,
			UserAgent:    "AI-Grading-Browser/1.0",
			WindowWidth:  1280,
			WindowHeight: 720,
		},
		Grading: GradingConfig{
			DefaultSubject:   "数学",
			DefaultCriteria:  "请根据标准答案评分",
			AnalysisMode:     "standard",
			MaxImageSize:     5 * 1024 * 1024, // 5MB
			SupportedFormats: []string{"jpg", "jpeg", "png", "webp"},
		},
	}
}

// GetConfigDir 获取配置目录
func GetConfigDir() (string, error) {
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("get user home directory: %w", err)
	}

	configDir := filepath.Join(homeDir, ".ai-grading")
	return configDir, nil
}

// GetDataDir 获取数据目录
func GetDataDir() (string, error) {
	configDir, err := GetConfigDir()
	if err != nil {
		return "", err
	}

	dataDir := filepath.Join(configDir, "data")
	return dataDir, nil
}

// GetLogsDir 获取日志目录
func GetLogsDir() (string, error) {
	configDir, err := GetConfigDir()
	if err != nil {
		return "", err
	}

	logsDir := filepath.Join(configDir, "logs")
	return logsDir, nil
}

// GetCacheDir 获取缓存目录
func GetCacheDir() (string, error) {
	configDir, err := GetConfigDir()
	if err != nil {
		return "", err
	}

	cacheDir := filepath.Join(configDir, "cache")
	return cacheDir, nil
}
