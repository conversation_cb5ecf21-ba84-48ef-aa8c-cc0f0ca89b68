# 软件界面草图

```text
+------------------------------------------------------------------------------------------------------+
| AI 自动阅卷助手 v1.0                                                                    [ - ] [口] [X] |
+------------------------------------------------------------------------------------------------------+
| 左侧: 配置区 (El-Card)                               | 右侧: 监控与预览区                               |
|------------------------------------------------------|----------------------------------------------|
|                                                      | [控制与状态] (El-Card)                       |
| [ 阅卷配置 ] (El-Form)                               |  阅卷数量： [ 12 ] 份(El-Input)               |
|                                                      |   当前状态   已批阅                           |
|    1. 浏览器自动化设置                                 |   +----------------------------------------+ |
|   ------------------------------------------------   |   |           开始阅卷 (El-Button)         | |
|   阅卷网址: [ https://exam.school.com/grade?id=... ] |   +----------------------------------------+ |
|             (El-Input)                               |                                              |
|                                                      | [ 实时预览 ] (El-Card)                       |
|   截图区域:                                          |                                              |
|   [ X:120, Y:300, W:800, H:600 ] (El-Input 只读)     |   +----------------------------------------+ |
|   <选择区域> (El-Button)   <预览> (El-Button)     |   |                                        | |
|                                                      |   |  (这里显示最新截取的答题区图片)        | |
|   分数输入框 Selector:                               |   |            (El-Image)                  | |
|   [ #score_input_id ] (El-Input)                     |   |                                        | |
|   <拾取元素> (El-Button)                             |   +----------------------------------------+ |
|                                                      |   AI分析结果:                                  |
|   翻页按钮 Selector:                                 |   [ 得分: 8。理由: 答出了“板块构造学说”和   ] |
|   [ .next-page-btn > span ] (El-Input)               |   [ “气候影响”，但未提及“洋流因素”。        ] |
|   <拾取元素> (El-Button)                             |   [ (只读文本区域)                           ] |
|                                                      |                                              |
|    2. AI 评分标准                                    | [ 运行日志 ] (El-Card with El-Tabs)          |
|   ------------------------------------------------   |                                              |
|   科    目: [ 高中地理 ] (el-select)                  |   [ 运行日志 | AI详细响应 ] (El-Tabs)         |
|                                                      |   |----------------------------------------| |
|   阅卷模式: [ 专业模式 ] (el-select)                  |   | [2023-10-27 10:30:01 INFO] 任务开始...  | |
|                                                      |   | [2023-10-27 10:30:03 INFO] 截图成功。   | |
|   评分标准:                                          |   | [2023-10-27 10:30:05 INFO] AI分析完成...  | |
|   [ 请根据以下标准和图片内容，给出0-10分... ]          |   | [2023-10-27 10:30:06 ERROR] 翻页失败...  | |
|   [ (一个可调整大小的多行文本框)            ]          |   | (一个可滚动的日志列表)                 | |
|   [ (El-Input type="textarea")             ]         |   +----------------------------------------+ |
|                                                      |                                              |
|                                                      |                                              |
+------------------------------------------------------------------------------------------------------+
```
